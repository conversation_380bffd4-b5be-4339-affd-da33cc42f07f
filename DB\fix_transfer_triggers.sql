-- <PERSON><PERSON>t untuk memperbaiki trigger transfer stok
-- Jalankan script ini untuk menerapkan perbaikan trigger

-- Drop trigger lama jika ada
DROP TRIGGER IF EXISTS `trg_transfer_kirim`;
DROP TRIGGER IF EXISTS `trg_transfer_terima`;

-- Buat ulang trigger untuk kirim transfer dengan perbaikan
DELIMITER //
CREATE TRIGGER `trg_transfer_kirim` AFTER UPDATE ON `transfer_stok` FOR EACH ROW BEGIN
    DECLARE nomor_keluar VARCHAR(50);
    DECLARE user_id INT DEFAULT 1;
    DECLARE detail_count INT DEFAULT 0;
    DECLARE barang_keluar_id BIGINT;

    -- <PERSON>ya jalankan jika status berubah dari draft ke dikirim
    IF OLD.status = 'draft' AND NEW.status = 'dikirim' THEN
        -- Cek apakah ada detail transfer
        SELECT COUNT(*) INTO detail_count 
        FROM transfer_stok_detail 
        WHERE transfer_stok_id = NEW.id;
        
        IF detail_count > 0 THEN
            -- Generate nomor barang keluar
            SET nomor_keluar = CONCAT('TK-', DATE_FORMAT(NEW.tanggal_kirim, '%Y%m%d'), '-', LPAD(NEW.id, 4, '0'));

            -- Cari user ID berdasarkan nama yang mengirim
            SELECT id_user INTO user_id 
            FROM tbl_user 
            WHERE full_name = NEW.dikirim_oleh OR username = NEW.dikirim_oleh 
            LIMIT 1;
            
            IF user_id IS NULL THEN
                SET user_id = 1; 
            END IF;

            -- Insert barang keluar header dengan status final
            INSERT INTO barang_keluar (
                nomor_pengeluaran, tanggal, jenis, ref_nomor, keterangan,
                status, total_item, total_qty, created_by, finalized_by, finalized_at
            ) VALUES (
                nomor_keluar,
                DATE(NEW.tanggal_kirim),
                'transfer_keluar',
                NEW.nomor_transfer,
                CONCAT('Transfer ke ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_tujuan_id)),
                'final',
                NEW.total_item,
                NEW.total_qty,
                user_id,
                user_id,
                NEW.tanggal_kirim
            );

            -- Ambil ID barang keluar yang baru dibuat
            SET barang_keluar_id = LAST_INSERT_ID();

            -- Insert detail barang keluar
            INSERT INTO barang_keluar_detail (
                id_barang_keluar, id_barang, id_gudang, qty_keluar, id_satuan, keterangan
            )
            SELECT
                barang_keluar_id,
                tsd.barang_id,
                NEW.gudang_asal_id,
                tsd.qty,
                tsd.satuan_id,
                CONCAT('Transfer ke gudang ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_tujuan_id), ' - ', COALESCE(tsd.keterangan, ''))
            FROM transfer_stok_detail tsd
            WHERE tsd.transfer_stok_id = NEW.id
            AND tsd.qty > 0;
        END IF;
    END IF;
END//
DELIMITER ;

-- Buat ulang trigger untuk terima transfer dengan perbaikan
DELIMITER //
CREATE TRIGGER `trg_transfer_terima` AFTER UPDATE ON `transfer_stok` FOR EACH ROW BEGIN
    DECLARE nomor_masuk VARCHAR(50);
    DECLARE user_id INT DEFAULT 1;
    DECLARE detail_count INT DEFAULT 0;
    DECLARE barang_masuk_id BIGINT;

    -- Hanya jalankan jika status berubah dari dikirim ke diterima
    IF OLD.status = 'dikirim' AND NEW.status = 'diterima' THEN
        -- Cek apakah ada detail transfer
        SELECT COUNT(*) INTO detail_count 
        FROM transfer_stok_detail 
        WHERE transfer_stok_id = NEW.id;
        
        IF detail_count > 0 THEN
            -- Generate nomor barang masuk
            SET nomor_masuk = CONCAT('TM-', DATE_FORMAT(NEW.tanggal_terima, '%Y%m%d'), '-', LPAD(NEW.id, 4, '0'));

            -- Cari user ID berdasarkan nama yang diterima
            SELECT id_user INTO user_id 
            FROM tbl_user 
            WHERE full_name = NEW.diterima_oleh OR username = NEW.diterima_oleh 
            LIMIT 1;
            
            IF user_id IS NULL THEN
                SET user_id = 1; 
            END IF;

            -- Insert barang masuk header dengan status final
            INSERT INTO barang_masuk (
                nomor_penerimaan, tanggal, jenis, ref_nomor, keterangan,
                status, total_item, total_qty, created_by, finalized_by, finalized_at
            ) VALUES (
                nomor_masuk,
                DATE(NEW.tanggal_terima),
                'transfer_masuk',
                NEW.nomor_transfer,
                CONCAT('Transfer dari ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_asal_id)),
                'final',
                NEW.total_item,
                NEW.total_qty,
                user_id,
                user_id,
                NEW.tanggal_terima
            );

            -- Ambil ID barang masuk yang baru dibuat
            SET barang_masuk_id = LAST_INSERT_ID();

            -- Insert detail barang masuk
            INSERT INTO barang_masuk_detail (
                id_barang_masuk, id_barang, id_gudang, qty_diterima, id_satuan, keterangan
            )
            SELECT
                barang_masuk_id,
                tsd.barang_id,
                NEW.gudang_tujuan_id,
                tsd.qty,
                tsd.satuan_id,
                CONCAT('Transfer dari gudang ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_asal_id), ' - ', COALESCE(tsd.keterangan, ''))
            FROM transfer_stok_detail tsd
            WHERE tsd.transfer_stok_id = NEW.id
            AND tsd.qty > 0;
        END IF;
    END IF;
END//
DELIMITER ;

-- Verifikasi trigger telah dibuat
SHOW TRIGGERS LIKE 'transfer_stok';
