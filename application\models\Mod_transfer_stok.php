<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Transfer Stok
 * Mengatur data transfer stok antar gudang
 */
class Mod_transfer_stok extends CI_Model
{
    var $table = 'transfer_stok';
    var $table_detail = 'transfer_stok_detail';
    var $column_search = array(
        'ts.nomor_transfer', 
        'ts.tanggal_transfer', 
        'ga.nama_gudang', 
        'gt.nama_gudang', 
        'ts.status', 
        'ts.keterangan'
    );
    var $column_order = array(
        'ts.id', 
        'ts.nomor_transfer', 
        'ts.tanggal_transfer', 
        'ga.nama_gudang', 
        'gt.nama_gudang', 
        'ts.status', 
        'ts.total_item',
        'ts.total_qty'
    );
    var $order = array('ts.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            ts.id,
            ts.nomor_transfer,
            ts.tanggal_transfer,
            ts.gudang_asal_id,
            ts.gudang_tujuan_id,
            ts.status,
            ts.total_item,
            ts.total_qty,
            ts.keterangan,
            ts.tanggal_kirim,
            ts.tanggal_terima,
            ts.dibuat_oleh,
            ts.dikirim_oleh,
            ts.diterima_oleh,
            ga.kode_gudang as kode_gudang_asal,
            ga.nama_gudang as nama_gudang_asal,
            gt.kode_gudang as kode_gudang_tujuan,
            gt.nama_gudang as nama_gudang_tujuan
        ');
        $this->db->from('transfer_stok ts');
        $this->db->join('gudang ga', 'ts.gudang_asal_id = ga.id', 'left');
        $this->db->join('gudang gt', 'ts.gudang_tujuan_id = gt.id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // ========== HEADER METHODS ==========

    // Insert header transfer stok
    function insert($data)
    {
        $insert = $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // Update header transfer stok
    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    // Get header transfer stok
    function get($id)
    {
        $this->db->select('
            ts.*,
            ga.kode_gudang as kode_gudang_asal,
            ga.nama_gudang as nama_gudang_asal,
            gt.kode_gudang as kode_gudang_tujuan,
            gt.nama_gudang as nama_gudang_tujuan
        ');
        $this->db->from('transfer_stok ts');
        $this->db->join('gudang ga', 'ts.gudang_asal_id = ga.id', 'left');
        $this->db->join('gudang gt', 'ts.gudang_tujuan_id = gt.id', 'left');
        $this->db->where('ts.id', $id);
        return $this->db->get()->row();
    }

    // Delete header transfer stok (cascade delete detail)
    function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Generate nomor transfer otomatis
    function generate_nomor()
    {
        $year = date('Y');
        $month = date('m');
        
        // Format: TR-YYYY-MM-XXX
        $prefix = "TR-{$year}-{$month}-";
        
        // Cari nomor terakhir bulan ini
        $this->db->select('nomor_transfer');
        $this->db->from($this->table);
        $this->db->like('nomor_transfer', $prefix, 'after');
        $this->db->order_by('nomor_transfer', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_nomor = $query->row()->nomor_transfer;
            $last_number = (int) substr($last_nomor, -3);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . str_pad($new_number, 3, '0', STR_PAD_LEFT);
    }

    // Check nomor exists
    function check_nomor_exists($nomor, $id = null)
    {
        $this->db->where('nomor_transfer', $nomor);
        if ($id) {
            $this->db->where('id !=', $id);
        }
        $query = $this->db->get($this->table);
        return $query->num_rows() > 0;
    }

    // ========== DETAIL METHODS ==========

    // Insert detail transfer stok
    function insert_detail($data)
    {
        $insert = $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    // Update detail transfer stok
    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    // Get detail transfer stok
    function get_detail($transfer_stok_id)
    {
        $this->db->select('
            tsd.*,
            b.kode_barang,
            b.nama_barang,
            s.kode_satuan,
            s.nama_satuan
        ');
        $this->db->from('transfer_stok_detail tsd');
        $this->db->join('barang b', 'tsd.barang_id = b.id', 'left');
        $this->db->join('satuan s', 'tsd.satuan_id = s.id', 'left');
        $this->db->where('tsd.transfer_stok_id', $transfer_stok_id);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Get detail by ID
    function get_detail_by_id($id)
    {
        $this->db->select('
            tsd.*,
            b.kode_barang,
            b.nama_barang,
            s.kode_satuan,
            s.nama_satuan
        ');
        $this->db->from('transfer_stok_detail tsd');
        $this->db->join('barang b', 'tsd.barang_id = b.id', 'left');
        $this->db->join('satuan s', 'tsd.satuan_id = s.id', 'left');
        $this->db->where('tsd.id', $id);
        return $this->db->get()->row();
    }

    // Delete detail transfer stok
    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    // Check if barang already exists in transfer detail
    function check_barang_exists_in_detail($transfer_stok_id, $barang_id, $exclude_id = null)
    {
        $this->db->where('transfer_stok_id', $transfer_stok_id);
        $this->db->where('barang_id', $barang_id);

        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }

        $query = $this->db->get($this->table_detail);
        return $query->num_rows() > 0;
    }

    // ========== WORKFLOW METHODS ==========

    // Kirim transfer (draft -> dikirim)
    function kirim_transfer($id, $user_kirim)
    {
        // Cek apakah masih draft
        $transfer = $this->get($id);
        if (!$transfer || $transfer->status != 'draft') {
            return array('valid' => false, 'message' => 'Transfer tidak dalam status draft');
        }

        // Cek apakah ada detail
        $detail_count = $this->db->where('transfer_stok_id', $id)->count_all_results($this->table_detail);
        if ($detail_count == 0) {
            return array('valid' => false, 'message' => 'Tidak ada detail barang untuk ditransfer');
        }

        // Validasi stok tersedia untuk semua item
        $validation_result = $this->validate_stock_availability($id);
        if (!$validation_result['valid']) {
            return $validation_result;
        }

        // Start transaction
        $this->db->trans_start();

        // Update status ke dikirim
        $data = array(
            'status' => 'dikirim',
            'dikirim_oleh' => $user_kirim,
            'tanggal_kirim' => date('Y-m-d H:i:s')
        );

        $this->db->where('id', $id);
        $this->db->update($this->table, $data);

        // Trigger database akan otomatis membuat barang_keluar dan stok_movement

        // Update referensi barang_keluar setelah trigger selesai
        $nomor_keluar = 'TK-' . date('Ymd', strtotime($data['tanggal_kirim'])) . '-' . str_pad($id, 4, '0', STR_PAD_LEFT);
        $this->db->where('id', $id);
        $this->db->update($this->table, array('ref_barang_keluar' => $nomor_keluar));

        // Complete transaction
        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            // Get database error for debugging
            $db_error = $this->db->error();
            log_message('error', 'Transfer stok kirim failed: ' . print_r($db_error, true));

            $error_message = 'Gagal memproses transfer stok';
            if (!empty($db_error['message'])) {
                $error_message .= ': ' . $db_error['message'];
            }

            return array('valid' => false, 'message' => $error_message);
        }

        return array('valid' => true, 'message' => 'Transfer berhasil dikirim dan stok telah dikurangi dari gudang asal');
    }

    // Terima transfer (dikirim -> diterima)
    function terima_transfer($id, $user_terima)
    {
        // Cek apakah dalam status dikirim
        $transfer = $this->get($id);
        if (!$transfer || $transfer->status != 'dikirim') {
            return array('valid' => false, 'message' => 'Transfer tidak dalam status dikirim');
        }

        // Cek apakah ada detail transfer
        $detail_count = $this->db->where('transfer_stok_id', $id)->count_all_results($this->table_detail);
        if ($detail_count == 0) {
            return array('valid' => false, 'message' => 'Tidak ada detail barang untuk diterima');
        }

        // Start transaction
        $this->db->trans_start();

        // Update status ke diterima
        $data = array(
            'status' => 'diterima',
            'diterima_oleh' => $user_terima,
            'tanggal_terima' => date('Y-m-d H:i:s')
        );

        $this->db->where('id', $id);
        $this->db->update($this->table, $data);

        // Cek apakah update berhasil
        if ($this->db->affected_rows() == 0) {
            $this->db->trans_rollback();
            return array('valid' => false, 'message' => 'Gagal mengupdate status transfer');
        }

        // Update referensi barang_masuk setelah trigger selesai
        $nomor_masuk = 'TM-' . date('Ymd', strtotime($data['tanggal_terima'])) . '-' . str_pad($id, 4, '0', STR_PAD_LEFT);
        $this->db->where('id', $id);
        $this->db->update($this->table, array('ref_barang_masuk' => $nomor_masuk));

        // Complete transaction
        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            // Get database error for debugging
            $db_error = $this->db->error();
            log_message('error', 'Transfer stok terima failed: ' . print_r($db_error, true));

            $error_message = 'Gagal memproses penerimaan transfer stok';
            if (!empty($db_error['message'])) {
                $error_message .= ': ' . $db_error['message'];
            }

            return array('valid' => false, 'message' => $error_message);
        }

        // Verifikasi bahwa barang_masuk telah dibuat
        $this->db->where('ref_nomor', $transfer->nomor_transfer);
        $this->db->where('jenis', 'transfer_masuk');
        $barang_masuk = $this->db->get('barang_masuk')->row();

        if (!$barang_masuk) {
            log_message('error', 'Barang masuk tidak terbuat untuk transfer ID: ' . $id);
            return array('valid' => false, 'message' => 'Transfer diterima tetapi barang masuk tidak terbuat. Silakan hubungi administrator.');
        }

        return array('valid' => true, 'message' => 'Transfer berhasil diterima dan stok telah ditambahkan ke gudang tujuan');
    }

    // Batalkan transfer
    function batal_transfer($id, $user_batal)
    {
        // Cek apakah masih bisa dibatalkan (draft atau dikirim)
        $transfer = $this->get($id);
        if (!$transfer || !in_array($transfer->status, ['draft', 'dikirim'])) {
            return array('valid' => false, 'message' => 'Transfer tidak dapat dibatalkan');
        }

        // Start transaction
        $this->db->trans_start();

        // Jika status dikirim, perlu membatalkan barang_keluar dan stok_movement
        if ($transfer->status == 'dikirim') {
            $this->cancel_barang_keluar($id);
        }

        // Update status ke batal
        $data = array(
            'status' => 'batal'
        );

        $this->db->where('id', $id);
        $this->db->update($this->table, $data);

        // Complete transaction
        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            // Get database error for debugging
            $db_error = $this->db->error();
            log_message('error', 'Transfer stok batal failed: ' . print_r($db_error, true));

            $error_message = 'Gagal membatalkan transfer stok';
            if (!empty($db_error['message'])) {
                $error_message .= ': ' . $db_error['message'];
            }

            return array('valid' => false, 'message' => $error_message);
        }

        $message = 'Transfer berhasil dibatalkan';
        if ($transfer->status == 'dikirim') {
            $message .= ' dan stok telah dikembalikan ke gudang asal';
        }

        return array('valid' => true, 'message' => $message);
    }

    // Validasi ketersediaan stok
    function validate_stock_availability($transfer_stok_id)
    {
        $details = $this->get_detail($transfer_stok_id);
        $transfer = $this->get($transfer_stok_id);
        $insufficient_items = array();

        foreach ($details as $detail) {
            // Cek stok tersedia di gudang asal
            $this->db->select('qty_terakhir');
            $this->db->from('stok_barang');
            $this->db->where('id_barang', $detail->barang_id);
            $this->db->where('id_gudang', $transfer->gudang_asal_id);
            $stok_query = $this->db->get();

            $stok_tersedia = 0;
            if ($stok_query->num_rows() > 0) {
                $stok_tersedia = $stok_query->row()->qty_terakhir;
            }

            if ($stok_tersedia < $detail->qty) {
                $insufficient_items[] = array(
                    'nama_barang' => $detail->nama_barang,
                    'qty_diminta' => $detail->qty,
                    'stok_tersedia' => $stok_tersedia,
                    'kekurangan' => $detail->qty - $stok_tersedia
                );
            }
        }

        if (!empty($insufficient_items)) {
            return array(
                'valid' => false,
                'message' => 'Stok tidak mencukupi untuk beberapa item',
                'insufficient_items' => $insufficient_items
            );
        }

        return array('valid' => true, 'message' => 'Stok mencukupi untuk semua item');
    }

    // ========== DROPDOWN METHODS ==========

    // Get gudang dropdown
    function get_gudang_dropdown()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'ASC');
        return $this->db->get()->result();
    }

    // Get barang dropdown
    function get_barang_dropdown()
    {
        $this->db->select('id, kode_barang, nama_barang, satuan_id');
        $this->db->from('barang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Get barang dropdown dengan stok di gudang tertentu
    function get_barang_with_stock($gudang_id)
    {
        $this->db->select('
            b.id,
            b.kode_barang,
            b.nama_barang,
            b.satuan_id,
            s.nama_satuan,
            COALESCE(sb.qty_terakhir, 0) as stok_tersedia
        ');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('stok_barang sb', 'b.id = sb.id_barang AND sb.id_gudang = ' . $gudang_id, 'left');
        $this->db->where('b.aktif', 1);
        $this->db->where('COALESCE(sb.qty_terakhir, 0) >', 0); // Hanya barang yang ada stoknya
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Get barang dropdown dengan stok yang tersedia (exclude yang sudah dipilih)
    function get_barang_with_stock_available($gudang_id, $transfer_stok_id)
    {
        $this->db->select('
            b.id,
            b.kode_barang,
            b.nama_barang,
            b.satuan_id,
            s.nama_satuan,
            COALESCE(sb.qty_terakhir, 0) as stok_tersedia
        ');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('stok_barang sb', 'b.id = sb.id_barang AND sb.id_gudang = ' . $gudang_id, 'left');

        // Exclude barang yang sudah ada di detail transfer (jika transfer_stok_id ada)
        if ($transfer_stok_id && $transfer_stok_id > 0) {
            $this->db->where('b.id NOT IN (
                SELECT barang_id FROM transfer_stok_detail
                WHERE transfer_stok_id = ' . $transfer_stok_id . '
            )');
        }

        $this->db->where('b.aktif', 1);
        $this->db->where('COALESCE(sb.qty_terakhir, 0) >', 0); // Hanya barang yang ada stoknya
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Get satuan dropdown
    function get_satuan_dropdown()
    {
        $this->db->select('id, kode_satuan, nama_satuan');
        $this->db->from('satuan');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_satuan', 'ASC');
        return $this->db->get()->result();
    }

    // Get barang detail untuk form
    function get_barang_detail($id_barang)
    {
        $this->db->select('id, kode_barang, nama_barang, merk, tipe, satuan_id');
        $this->db->from('barang');
        $this->db->where('id', $id_barang);
        $this->db->where('aktif', 1);
        return $this->db->get()->row();
    }

    // Get stok barang di gudang tertentu
    function get_stok_barang($id_barang, $id_gudang)
    {
        $this->db->select('qty_terakhir');
        $this->db->from('stok_barang');
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->row()->qty_terakhir;
        }
        return 0;
    }

    // ========== TRANSFER CANCELLATION METHODS ==========

    // Batalkan barang keluar saat transfer dibatalkan
    function cancel_barang_keluar($transfer_stok_id)
    {
        $transfer = $this->get($transfer_stok_id);

        if ($transfer && $transfer->ref_barang_keluar) {
            // Get barang_keluar ID first
            $this->db->select('id');
            $this->db->where('nomor_pengeluaran', $transfer->ref_barang_keluar);
            $barang_keluar = $this->db->get('barang_keluar')->row();

            if ($barang_keluar) {
                // Hapus stok movement yang terkait dengan transfer keluar
                $this->db->where('ref_transaksi', $transfer->ref_barang_keluar);
                $this->db->delete('stok_movement');

                // Hapus detail barang keluar menggunakan ID langsung
                $this->db->where('id_barang_keluar', $barang_keluar->id);
                $this->db->delete('barang_keluar_detail');

                // Hapus barang keluar
                $this->db->where('id', $barang_keluar->id);
                $this->db->delete('barang_keluar');

                // Buat stok movement untuk mengembalikan stok (reverse)
                $details = $this->get_detail($transfer_stok_id);
                foreach ($details as $detail) {
                    $movement_data = array(
                        'tanggal' => date('Y-m-d H:i:s'),
                        'id_barang' => $detail->barang_id,
                        'id_gudang' => $transfer->gudang_asal_id,
                        'tipe_transaksi' => 'penyesuaian',
                        'qty_in' => $detail->qty,
                        'qty_out' => 0,
                        'keterangan' => 'Pembatalan Transfer - ' . $transfer->nomor_transfer,
                        'ref_transaksi' => 'CANCEL-' . $transfer->nomor_transfer,
                        'user_input' => 'system'
                    );
                    $this->db->insert('stok_movement', $movement_data);
                }
            }
        }
    }

    // ========== DEBUGGING AND MONITORING METHODS ==========

    // Cek konsistensi data transfer stok
    function check_transfer_consistency($transfer_stok_id)
    {
        $transfer = $this->get($transfer_stok_id);
        $issues = array();

        if (!$transfer) {
            return array('valid' => false, 'message' => 'Transfer tidak ditemukan');
        }

        // Cek detail transfer
        $details = $this->get_detail($transfer_stok_id);
        if (empty($details)) {
            $issues[] = 'Tidak ada detail transfer';
        }

        // Jika status dikirim, cek barang_keluar
        if ($transfer->status == 'dikirim' || $transfer->status == 'diterima') {
            if ($transfer->ref_barang_keluar) {
                $this->db->where('nomor_pengeluaran', $transfer->ref_barang_keluar);
                $barang_keluar = $this->db->get('barang_keluar')->row();

                if (!$barang_keluar) {
                    $issues[] = 'Barang keluar tidak ditemukan: ' . $transfer->ref_barang_keluar;
                } else {
                    // Cek detail barang keluar
                    $this->db->where('id_barang_keluar', $barang_keluar->id);
                    $keluar_details = $this->db->get('barang_keluar_detail')->result();

                    if (count($keluar_details) != count($details)) {
                        $issues[] = 'Jumlah detail barang keluar tidak sesuai';
                    }
                }
            } else {
                $issues[] = 'Referensi barang keluar kosong';
            }
        }

        // Jika status diterima, cek barang_masuk
        if ($transfer->status == 'diterima') {
            if ($transfer->ref_barang_masuk) {
                $this->db->where('nomor_penerimaan', $transfer->ref_barang_masuk);
                $barang_masuk = $this->db->get('barang_masuk')->row();

                if (!$barang_masuk) {
                    $issues[] = 'Barang masuk tidak ditemukan: ' . $transfer->ref_barang_masuk;
                } else {
                    // Cek detail barang masuk
                    $this->db->where('id_barang_masuk', $barang_masuk->id);
                    $masuk_details = $this->db->get('barang_masuk_detail')->result();

                    if (count($masuk_details) != count($details)) {
                        $issues[] = 'Jumlah detail barang masuk tidak sesuai';
                    }
                }
            } else {
                $issues[] = 'Referensi barang masuk kosong';
            }
        }

        return array(
            'valid' => empty($issues),
            'issues' => $issues,
            'transfer' => $transfer,
            'details_count' => count($details)
        );
    }

    // Get stok movement history untuk transfer
    function get_transfer_stock_movements($transfer_stok_id)
    {
        $transfer = $this->get($transfer_stok_id);
        $movements = array();

        if ($transfer) {
            // Movement untuk barang keluar
            if ($transfer->ref_barang_keluar) {
                $this->db->select('sm.*, b.nama_barang, g.nama_gudang');
                $this->db->from('stok_movement sm');
                $this->db->join('barang b', 'sm.id_barang = b.id', 'left');
                $this->db->join('gudang g', 'sm.id_gudang = g.id', 'left');
                $this->db->where('sm.ref_transaksi', $transfer->ref_barang_keluar);
                $this->db->order_by('sm.tanggal', 'ASC');
                $movements['keluar'] = $this->db->get()->result();
            }

            // Movement untuk barang masuk
            if ($transfer->ref_barang_masuk) {
                $this->db->select('sm.*, b.nama_barang, g.nama_gudang');
                $this->db->from('stok_movement sm');
                $this->db->join('barang b', 'sm.id_barang = b.id', 'left');
                $this->db->join('gudang g', 'sm.id_gudang = g.id', 'left');
                $this->db->where('sm.ref_transaksi', $transfer->ref_barang_masuk);
                $this->db->order_by('sm.tanggal', 'ASC');
                $movements['masuk'] = $this->db->get()->result();
            }
        }

        return $movements;
    }
}

